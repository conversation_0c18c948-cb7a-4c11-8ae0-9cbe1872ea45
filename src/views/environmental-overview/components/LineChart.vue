<template>
  <div
    ref="chartRef"
    :style="`width: 100%; height: ${height}px;`"
    @click="clickHandle"
  ></div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onBeforeUnmount } from 'vue';
import * as echarts from 'echarts';

interface Props {
  chartData: number[];
  timeData: string[];
  title: string;
  height: number;
  pointValue: number;
  type: string;
  siteId: string;
  monitorType: string;
  legend: string;
}

const props = withDefaults(defineProps<Props>(), {
  chartData: () => [],
  timeData: () => [],
  title: '',
  height: 300,
  pointValue: 0,
  type: '',
  siteId: '',
  monitorType: '',
  legend: '',
});

const emit = defineEmits<{
  childChart: [data: { hour: number; type: string; siteId: string; monitorType: string }];
}>();

const chartRef = ref<HTMLElement>();
let myChart: echarts.ECharts | null = null;

const initChart = () => {
  if (!chartRef.value) return;
  
  // 基于准备好的dom，初始化echarts实例
  const chartDom = chartRef.value;
  myChart = echarts.init(chartDom);
  
  const legendValue = props.legend === '' || props.legend === undefined || props.legend === null 
    ? props.title 
    : props.legend;

  // 指定图表的配置项和数据
  const option = {
    title: {
      text: legendValue,
      textStyle: {
        color: '#fff',
      },
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: [props.type],
      bottom: '5%',
      left: 'center',
      icon: 'circle',
      textStyle: {
        color: '#fff',
      },
    },
    xAxis: {
      type: 'category',
      data: props.timeData,
      axisLabel: {
        color: '#99b7d2', // y轴字体颜色
      },
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        color: '#99b7d2', // y轴字体颜色
      },
    },
    series: [
      {
        name: props.type,
        type: 'line',
        data: props.chartData,
        areaStyle: {
          color: 'rgba(83, 240, 233, 0.3)',
        },
        smooth: 0.6,
        showSymbol: false,
        itemStyle: {
          color: 'rgb(83, 240, 233)', // 设置拐点的颜色为红色
        },
        lineStyle: {
          color: 'rgb(83, 240, 233)',
        },
        label: {
          formatter: '{b}：{d}%',
        },
        markPoint: {
          data: [
            { name: '预警值', value: props.pointValue }
          ]
        },
        // 添加红色预警线
        markLine: {
          data: props.pointValue === 0 ? [] : [
            {
              name: '红色预警线',
              yAxis: props.pointValue, // 预警线的值
              lineStyle: {
                color: 'red', // 预警线颜色
                type: 'dashed', // 虚线样式
                width: 2 // 线宽
              },
              label: {
                formatter: '预警', // 预警线标签
                position: 'end', // 标签位置
                color: 'red' // 标签颜色
              }
            }
          ]
        }
      }
    ]
  };

  // 使用刚指定的配置项和数据显示图表。
  myChart.setOption(option);
};

const clickHandle = () => {
  emit('childChart', {
    hour: 24,
    type: props.type,
    siteId: props.siteId,
    monitorType: props.monitorType
  });
};

// 监听数据变化
watch(() => props.chartData, () => {
  console.log('变化-chartData');
  initChart();
}, { deep: true });

onMounted(() => {
  initChart();
});

onBeforeUnmount(() => {
  if (myChart) {
    myChart.dispose();
    myChart = null;
  }
});
</script>

<style scoped>
/* 你可以在这里添加一些样式 */
</style>