<template>
  <div class="main" :style="{ width: !isCollapsed ? '175px' : width }">
    <a-card class="box-card">
      <template #title>
        <div class="clearfix">
          <span style="color: #ffffff; font-size: 14px">{{ title }}</span>
          <img
            src="../assets/images/weizhankaigaoliang.png"
            alt=""
            :class="['img', !isCollapsed ? 'routeImg' : '']"
            @click="toggleCollapse"
          />
        </div>
      </template>
      <div
        :class="['content', !isCollapsed ? 'collapse-content' : '']"
        :style="{
          height: !isCollapsed ? '0px' : height !== '' ? height : 'auto',
        }"
      >
        <div style="padding: 16px; position: relative; height: 100%">
          <slot name="content" :is-collapsed="isCollapsed" />
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

interface Props {
  title?: string;
  width?: string;
  height?: string;
}

interface Emits {
  (e: 'child', data: { queryParam: boolean }): void;
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  width: '',
  height: '',
});

const emit = defineEmits<Emits>();

const isCollapsed = ref(false);

const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
  emit('child', { queryParam: isCollapsed.value });
};
</script>

<style scoped>
.box-card {
  width: 100%;
  background-color: transparent;
  border: none;
}
.content {
  height: 280px;
  transition: height 0.2s;
  background: rgba(0, 43, 104, 0.5);
}
.collapse-content {
  height: 0px;
}
.solt-content {
  padding: 16px;
}
:deep(.ant-card-body) {
  padding: 0px;
}
.clearfix {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.clearfix:after {
  display: none;
}
.img {
  width: 11px;
  height: 11px;
  transform: rotate(90deg);
  transition: transform 0.2s;
  cursor: pointer;
}
.routeImg {
  transform: rotate(270deg);
}
:deep(.ant-card-head) {
  height: 40px;
  padding: 0px 15px;
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #ffffff;
  border: none;
  background: linear-gradient(
    to bottom,
    rgba(0, 106, 255, 1),
    rgba(0, 43, 104, 0.5)
  );
}
</style>
